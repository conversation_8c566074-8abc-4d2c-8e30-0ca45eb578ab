"""
全局上下文中间件
负责从请求中轻量级地解析source标识，不执行任何数据库查询或复杂业务逻辑
"""
import logging
from typing import Optional
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

logger = logging.getLogger(__name__)


class GlobalContextMiddleware(BaseHTTPMiddleware):
    """
    全局上下文中间件
    
    职责：
    1. 从请求中解析source标识
    2. 将source存储到request.state中
    3. 不执行任何数据库查询或复杂业务逻辑
    """
    
    def __init__(self, app):
        super().__init__(app)
        # 不需要商户验证的路径
        self.exempt_paths = {
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/health",
            "/favicon.ico"
        }
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求"""
        # 检查是否为豁免路径
        if any(request.url.path.startswith(path) for path in self.exempt_paths):
            return await call_next(request)
        
        # 从请求中提取source标识
        app_source = self._extract_app_source(request)
        
        # 将source存储到request.state中，供后续依赖注入使用
        request.state.app_source = app_source
        
        # 记录日志（可选）
        if app_source:
            logger.debug(f"提取到app_source: {app_source}")
        else:
            logger.debug("未提取到app_source，将使用默认值")
        
        # 继续处理请求
        response = await call_next(request)
        return response
    
    def _extract_app_source(self, request: Request) -> Optional[str]:
        """
        从请求中提取app source标识
        
        优先级：
        1. X-App-Source 请求头
        2. source 查询参数
        3. X-Merchant-Source 请求头（兼容性）
        4. 返回None（由依赖注入处理默认值或错误）
        """
        # 1. 优先从X-App-Source请求头获取
        app_source = request.headers.get("X-App-Source")
        if app_source:
            return app_source.strip()
        
        # 2. 从source查询参数获取
        app_source = request.query_params.get("source")
        if app_source:
            return app_source.strip()
        
        # 3. 从X-Merchant-Source请求头获取（兼容性）
        app_source = request.headers.get("X-Merchant-Source")
        if app_source:
            return app_source.strip()
        
        # 4. 从路径参数获取（特殊情况）
        path_parts = request.url.path.strip("/").split("/")
        if len(path_parts) > 1 and path_parts[0] == "merchant":
            return path_parts[1]
        
        # 返回None，让依赖注入处理
        return None
