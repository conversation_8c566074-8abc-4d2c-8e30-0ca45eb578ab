"""
中间件包初始化文件
"""
# 新的轻量级中间件
from .middleware import GlobalContextMiddleware

# 旧的商户中间件（兼容性）
from .merchant_middleware import (
    MerchantContextMiddleware,
    get_current_merchant,
    get_current_merchant_id,
    get_current_merchant_code,
    get_current_wechat_config,
    require_merchant_context,
    MerchantContextManager,
    with_merchant_context,
    extract_merchant_from_request
)

__all__ = [
    # 新的中间件
    "GlobalContextMiddleware",
    # 旧的中间件（兼容性）
    "MerchantContextMiddleware",
    "get_current_merchant",
    "get_current_merchant_id",
    "get_current_merchant_code",
    "get_current_wechat_config",
    "require_merchant_context",
    "MerchantContextManager",
    "with_merchant_context",
    "extract_merchant_from_request"
]
