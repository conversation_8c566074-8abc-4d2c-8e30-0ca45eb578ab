"""
商户统计服务
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.models import User
from app.models.payment import Order, PaymentRecord, UserMembership, OrderStatus, PaymentStatus
from app.models.merchant import Merchant
# 移除对旧中间件的依赖，改为通过参数传递merchant_id

logger = logging.getLogger(__name__)


class MerchantStatsService:
    """商户统计服务"""
    
    def get_merchant_overview(self, db: Session, merchant_id: int) -> Dict[str, Any]:
        """获取商户概览统计"""
        if not merchant_id:
            raise ValueError("缺少商户ID")
        
        try:
            # 用户统计
            total_users = db.query(User).filter(User.merchant_id == merchant_id).count()
            
            # 订单统计
            total_orders = db.query(Order).filter(Order.merchant_id == merchant_id).count()
            paid_orders = db.query(Order).filter(
                and_(
                    Order.merchant_id == merchant_id,
                    Order.status == OrderStatus.PAID
                )
            ).count()
            
            # 交易金额统计
            total_amount_result = db.query(func.sum(Order.amount)).filter(
                and_(
                    Order.merchant_id == merchant_id,
                    Order.status == OrderStatus.PAID
                )
            ).scalar()
            total_amount = float(total_amount_result or 0) / 100  # 转换为元
            
            # 会员统计
            active_members = db.query(UserMembership).filter(
                and_(
                    UserMembership.merchant_id == merchant_id,
                    UserMembership.is_active == True,
                    UserMembership.end_date > datetime.now()
                )
            ).count()
            
            # 今日统计
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_orders = db.query(Order).filter(
                and_(
                    Order.merchant_id == merchant_id,
                    Order.created_at >= today_start
                )
            ).count()
            
            today_amount_result = db.query(func.sum(Order.amount)).filter(
                and_(
                    Order.merchant_id == merchant_id,
                    Order.status == OrderStatus.PAID,
                    Order.created_at >= today_start
                )
            ).scalar()
            today_amount = float(today_amount_result or 0) / 100
            
            return {
                "merchant_id": merchant_id,
                "total_users": total_users,
                "total_orders": total_orders,
                "paid_orders": paid_orders,
                "total_amount": total_amount,
                "active_members": active_members,
                "today_orders": today_orders,
                "today_amount": today_amount,
                "conversion_rate": round(paid_orders / total_orders * 100, 2) if total_orders > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取商户概览统计失败: {e}")
            raise
    
    def get_daily_stats(
        self,
        db: Session,
        merchant_id: int,
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """获取每日统计数据"""
        if not merchant_id:
            raise ValueError("缺少商户ID")
        
        try:
            stats = []
            for i in range(days):
                date = datetime.now().date() - timedelta(days=i)
                date_start = datetime.combine(date, datetime.min.time())
                date_end = datetime.combine(date, datetime.max.time())
                
                # 当日订单数
                daily_orders = db.query(Order).filter(
                    and_(
                        Order.merchant_id == merchant_id,
                        Order.created_at >= date_start,
                        Order.created_at <= date_end
                    )
                ).count()
                
                # 当日支付订单数
                daily_paid_orders = db.query(Order).filter(
                    and_(
                        Order.merchant_id == merchant_id,
                        Order.status == OrderStatus.PAID,
                        Order.created_at >= date_start,
                        Order.created_at <= date_end
                    )
                ).count()
                
                # 当日交易金额
                daily_amount_result = db.query(func.sum(Order.amount)).filter(
                    and_(
                        Order.merchant_id == merchant_id,
                        Order.status == OrderStatus.PAID,
                        Order.created_at >= date_start,
                        Order.created_at <= date_end
                    )
                ).scalar()
                daily_amount = float(daily_amount_result or 0) / 100
                
                # 当日新用户数
                daily_new_users = db.query(User).filter(
                    and_(
                        User.merchant_id == merchant_id,
                        User.created_at >= date_start,
                        User.created_at <= date_end
                    )
                ).count()
                
                stats.append({
                    "date": date.isoformat(),
                    "orders": daily_orders,
                    "paid_orders": daily_paid_orders,
                    "amount": daily_amount,
                    "new_users": daily_new_users,
                    "conversion_rate": round(daily_paid_orders / daily_orders * 100, 2) if daily_orders > 0 else 0
                })
            
            return list(reversed(stats))  # 按时间正序返回
            
        except Exception as e:
            logger.error(f"获取每日统计失败: {e}")
            raise
    
    def get_user_stats(self, db: Session, merchant_id: int) -> Dict[str, Any]:
        """获取用户统计"""
        if not merchant_id:
            raise ValueError("缺少商户ID")
        
        try:
            # 总用户数
            total_users = db.query(User).filter(User.merchant_id == merchant_id).count()
            
            # 会员用户数
            member_users = db.query(User).filter(
                and_(
                    User.merchant_id == merchant_id,
                    User.is_member == True
                )
            ).count()
            
            # 活跃会员数（有效期内）
            active_members = db.query(UserMembership).filter(
                and_(
                    UserMembership.merchant_id == merchant_id,
                    UserMembership.is_active == True,
                    UserMembership.end_date > datetime.now()
                )
            ).count()
            
            # 本月新增用户
            month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_new_users = db.query(User).filter(
                and_(
                    User.merchant_id == merchant_id,
                    User.created_at >= month_start
                )
            ).count()
            
            # 付费用户数（有过成功支付的用户）
            paid_users = db.query(User).join(Order).filter(
                and_(
                    User.merchant_id == merchant_id,
                    Order.status == OrderStatus.PAID
                )
            ).distinct().count()
            
            return {
                "total_users": total_users,
                "member_users": member_users,
                "active_members": active_members,
                "monthly_new_users": monthly_new_users,
                "paid_users": paid_users,
                "member_rate": round(member_users / total_users * 100, 2) if total_users > 0 else 0,
                "paid_user_rate": round(paid_users / total_users * 100, 2) if total_users > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            raise
    
    def get_payment_stats(self, db: Session, merchant_id: int) -> Dict[str, Any]:
        """获取支付统计"""
        if not merchant_id:
            raise ValueError("缺少商户ID")
        
        try:
            # 总订单数
            total_orders = db.query(Order).filter(Order.merchant_id == merchant_id).count()
            
            # 各状态订单数
            pending_orders = db.query(Order).filter(
                and_(Order.merchant_id == merchant_id, Order.status == OrderStatus.PENDING)
            ).count()
            
            paid_orders = db.query(Order).filter(
                and_(Order.merchant_id == merchant_id, Order.status == OrderStatus.PAID)
            ).count()
            
            cancelled_orders = db.query(Order).filter(
                and_(Order.merchant_id == merchant_id, Order.status == OrderStatus.CANCELLED)
            ).count()
            
            expired_orders = db.query(Order).filter(
                and_(Order.merchant_id == merchant_id, Order.status == OrderStatus.EXPIRED)
            ).count()
            
            # 交易金额统计
            total_amount_result = db.query(func.sum(Order.amount)).filter(
                and_(Order.merchant_id == merchant_id, Order.status == OrderStatus.PAID)
            ).scalar()
            total_amount = float(total_amount_result or 0) / 100
            
            # 平均订单金额
            avg_amount = total_amount / paid_orders if paid_orders > 0 else 0
            
            # 退款统计
            refund_amount_result = db.query(func.sum(PaymentRecord.refund_amount)).filter(
                and_(
                    PaymentRecord.merchant_id == merchant_id,
                    PaymentRecord.status == PaymentStatus.REFUNDED
                )
            ).scalar()
            refund_amount = float(refund_amount_result or 0) / 100
            
            return {
                "total_orders": total_orders,
                "pending_orders": pending_orders,
                "paid_orders": paid_orders,
                "cancelled_orders": cancelled_orders,
                "expired_orders": expired_orders,
                "total_amount": total_amount,
                "avg_amount": round(avg_amount, 2),
                "refund_amount": refund_amount,
                "success_rate": round(paid_orders / total_orders * 100, 2) if total_orders > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取支付统计失败: {e}")
            raise
    
    def get_all_merchants_stats(self, db: Session) -> List[Dict[str, Any]]:
        """获取所有商户统计（管理员用）"""
        try:
            merchants = db.query(Merchant).all()
            stats = []
            
            for merchant in merchants:
                merchant_stats = self.get_merchant_overview(db, merchant.id)
                merchant_stats.update({
                    "merchant_code": merchant.merchant_code,
                    "merchant_name": merchant.merchant_name,
                    "status": merchant.status.value
                })
                stats.append(merchant_stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取所有商户统计失败: {e}")
            raise


# 创建全局统计服务实例
merchant_stats_service = MerchantStatsService()
